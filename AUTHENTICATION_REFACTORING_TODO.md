# 🔐 Authentication Refactoring TODO List
## Nudron Flutter Water Metering App

> **Objective:** Refactor the authentication system to follow SOLID and DRY principles, implementing Clean Architecture patterns for maintainable, testable, and scalable authentication.

---

## 📊 **Progress Overview**

| Priority | Total Items | Completed | In Progress | Not Started |
|----------|-------------|-----------|-------------|-------------|
| High     | 5           | 0         | 0           | 5           |
| Medium   | 5           | 0         | 0           | 5           |
| Low      | 4           | 0         | 0           | 4           |
| **Total** | **14**     | **0**     | **0**       | **14**      |

---

## 🔥 **HIGH PRIORITY ITEMS**

### 1. Create Authentication Domain Layer
- [ ] **Task 1.1:** Create domain entities
  - [ ] `lib/domain/entities/auth_user.dart`
  - [ ] `lib/domain/entities/auth_session.dart`
  - [ ] `lib/domain/entities/auth_result.dart`
- [ ] **Task 1.2:** Create repository abstractions
  - [ ] `lib/domain/repositories/auth_repository.dart`
- [ ] **Task 1.3:** Create use cases
  - [ ] `lib/domain/usecases/login_usecase.dart`
  - [ ] `lib/domain/usecases/logout_usecase.dart`
  - [ ] `lib/domain/usecases/verify_two_factor_usecase.dart`

**Files Affected:** `lib/view_model/loginPostRequests.dart` (refactor)  
**Assignee:** _TBD_  
**Estimated Effort:** 3-4 days  
**Complexity:** High  
**Dependencies:** None  

**Expected Outcome:** Clean separation between domain and implementation layers

<details>
<summary>📋 Implementation Details</summary>

```dart
// lib/domain/repositories/auth_repository.dart
abstract class AuthRepository {
  Future<AuthResult> login(String email, String password);
  Future<AuthResult> loginWithBiometric(String email);
  Future<void> logout();
  Future<void> globalLogout();
  Future<AuthResult> verifyTwoFactor(String refCode, String code);
  Future<String> refreshToken();
  Future<bool> isAuthenticated();
  Future<AuthUser?> getCurrentUser();
}

// lib/domain/entities/auth_result.dart
class AuthResult {
  final bool success;
  final AuthUser? user;
  final String? twoFactorRefCode;
  final String? error;
  final bool requiresTwoFactor;
  final String? accessToken;
  final String? refreshToken;

  const AuthResult({
    required this.success,
    this.user,
    this.twoFactorRefCode,
    this.error,
    this.requiresTwoFactor = false,
    this.accessToken,
    this.refreshToken,
  });
}
```
</details>

---

### 2. Implement Dedicated AuthBloc
- [ ] **Task 2.1:** Create AuthBloc structure
  - [ ] `lib/bloc/auth/auth_bloc.dart`
  - [ ] `lib/bloc/auth/auth_event.dart`
  - [ ] `lib/bloc/auth/auth_state.dart`
- [ ] **Task 2.2:** Extract auth logic from DashboardBloc
  - [ ] Remove auth methods from `lib/bloc/dashboardBloc/dashboardBloc.dart`
  - [ ] Update DashboardBloc to depend on AuthBloc
- [ ] **Task 2.3:** Update UI to use AuthBloc
  - [ ] Update `lib/views/pages/LoginPage2.dart`
  - [ ] Update `lib/views/pages/EnterTwoFacCode.dart`

**Files Affected:** `lib/bloc/dashboardBloc/dashboardBloc.dart`, Login pages  
**Assignee:** _TBD_  
**Estimated Effort:** 4-5 days  
**Complexity:** High  
**Dependencies:** Task 1 (Domain Layer)  

**Expected Outcome:** Single responsibility for authentication state management

<details>
<summary>📋 Implementation Details</summary>

```dart
// lib/bloc/auth/auth_state.dart
abstract class AuthState extends Equatable {
  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {}
class AuthLoading extends AuthState {}
class AuthAuthenticated extends AuthState {
  final AuthUser user;
  AuthAuthenticated(this.user);
  @override
  List<Object?> get props => [user];
}
class AuthUnauthenticated extends AuthState {}
class AuthTwoFactorRequired extends AuthState {
  final String refCode;
  AuthTwoFactorRequired(this.refCode);
  @override
  List<Object?> get props => [refCode];
}
class AuthError extends AuthState {
  final String message;
  AuthError(this.message);
  @override
  List<Object?> get props => [message];
}

// lib/bloc/auth/auth_event.dart
abstract class AuthEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class AuthLoginRequested extends AuthEvent {
  final String email, password;
  AuthLoginRequested(this.email, this.password);
  @override
  List<Object?> get props => [email, password];
}

class AuthBiometricLoginRequested extends AuthEvent {
  final String email;
  AuthBiometricLoginRequested(this.email);
  @override
  List<Object?> get props => [email];
}

class AuthTwoFactorSubmitted extends AuthEvent {
  final String refCode, code;
  AuthTwoFactorSubmitted(this.refCode, this.code);
  @override
  List<Object?> get props => [refCode, code];
}

class AuthLogoutRequested extends AuthEvent {}
class AuthCheckRequested extends AuthEvent {}
```
</details>

---

### 3. Implement Dependency Injection Container
- [ ] **Task 3.1:** Setup GetIt dependency injection
  - [ ] Add `get_it` package to `pubspec.yaml`
  - [ ] Create `lib/core/di/injection_container.dart`
- [ ] **Task 3.2:** Register dependencies
  - [ ] Register BLoCs, repositories, services
  - [ ] Setup dependency graph
- [ ] **Task 3.3:** Initialize DI in main.dart
  - [ ] Update `lib/main.dart` to setup DI
  - [ ] Update widget tree to use injected dependencies

**Files Affected:** `lib/main.dart`, All BLoCs and services  
**Assignee:** _TBD_  
**Estimated Effort:** 2-3 days  
**Complexity:** High  
**Dependencies:** Task 1, 2  

**Expected Outcome:** Proper dependency inversion and testable architecture

<details>
<summary>📋 Implementation Details</summary>

```dart
// lib/core/di/injection_container.dart
import 'package:get_it/get_it.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // BLoCs
  sl.registerFactory(() => AuthBloc(sl()));
  sl.registerFactory(() => DashboardBloc(sl()));

  // Use cases
  sl.registerLazySingleton(() => LoginUseCase(sl()));
  sl.registerLazySingleton(() => LogoutUseCase(sl()));
  sl.registerLazySingleton(() => VerifyTwoFactorUseCase(sl()));

  // Repositories
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(sl(), sl())
  );

  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(sl())
  );
  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(sl())
  );

  // Services
  sl.registerLazySingleton<TokenService>(
    () => TokenServiceImpl(sl(), sl())
  );
  sl.registerLazySingleton<BiometricService>(
    () => BiometricServiceImpl(sl())
  );

  // External
  sl.registerLazySingleton(() => const FlutterSecureStorage());
  sl.registerLazySingleton(() => http.Client());
  sl.registerLazySingleton(() => LocalAuthentication());
}
```
</details>

---

### 4. Create Authentication Data Layer
- [ ] **Task 4.1:** Implement repository pattern
  - [ ] `lib/data/repositories/auth_repository_impl.dart`
- [ ] **Task 4.2:** Create data sources
  - [ ] `lib/data/datasources/auth_remote_datasource.dart`
  - [ ] `lib/data/datasources/auth_local_datasource.dart`
- [ ] **Task 4.3:** Create data models
  - [ ] `lib/data/models/auth_user_model.dart`
  - [ ] `lib/data/models/auth_session_model.dart`

**Files Affected:** New data layer files  
**Assignee:** _TBD_  
**Estimated Effort:** 3-4 days  
**Complexity:** High  
**Dependencies:** Task 1 (Domain Layer)  

**Expected Outcome:** Clean separation of data access concerns

---

### 5. Standardize Error Handling Patterns
- [ ] **Task 5.1:** Create error abstractions
  - [ ] `lib/core/error/auth_exceptions.dart`
  - [ ] `lib/core/error/auth_failures.dart`
- [ ] **Task 5.2:** Implement error mapping
  - [ ] Map exceptions to failures in repository
  - [ ] Update BLoC to handle failures
- [ ] **Task 5.3:** Update UI error handling
  - [ ] Consistent error display across auth flows
  - [ ] User-friendly error messages

**Files Affected:** All auth-related files  
**Assignee:** _TBD_  
**Estimated Effort:** 2-3 days  
**Complexity:** Medium  
**Dependencies:** Task 2, 4  

**Expected Outcome:** Consistent error handling across authentication flows

---

## 🔶 **MEDIUM PRIORITY ITEMS**

### 6. Refactor Token Management
- [ ] **Task 6.1:** Create token service abstraction
  - [ ] `lib/core/services/token_service.dart`
- [ ] **Task 6.2:** Implement secure storage service
  - [ ] `lib/core/services/secure_storage_service.dart`
- [ ] **Task 6.3:** Update existing token logic
  - [ ] Refactor `lib/view_model/loginPostRequests.dart`

**Files Affected:** `lib/view_model/loginPostRequests.dart`  
**Assignee:** _TBD_  
**Estimated Effort:** 2-3 days  
**Complexity:** Medium  
**Dependencies:** Task 3 (DI Container)  

---

### 7. Implement Session Management Service
- [ ] **Task 7.1:** Create session service
  - [ ] `lib/core/services/session_service.dart`
- [ ] **Task 7.2:** Update session handling
  - [ ] Remove session logic from DashboardBloc
  - [ ] Implement proper session lifecycle

**Files Affected:** `lib/bloc/dashboardBloc/dashboardBloc.dart`  
**Assignee:** _TBD_  
**Estimated Effort:** 2-3 days  
**Complexity:** Medium  
**Dependencies:** Task 2 (AuthBloc)  

---

### 8. Create Biometric Authentication Abstraction
- [ ] **Task 8.1:** Create biometric service interface
  - [ ] `lib/domain/services/biometric_service.dart`
- [ ] **Task 8.2:** Implement biometric service
  - [ ] `lib/data/services/biometric_service_impl.dart`
- [ ] **Task 8.3:** Refactor BiometricHelper
  - [ ] Update `lib/utils/biometric_helper.dart`

**Files Affected:** `lib/utils/biometric_helper.dart`  
**Assignee:** _TBD_  
**Estimated Effort:** 2-3 days  
**Complexity:** Medium  
**Dependencies:** Task 1 (Domain Layer)  

---

### 9. Refactor Two-Factor Authentication
- [ ] **Task 9.1:** Create 2FA service
  - [ ] `lib/domain/services/two_factor_service.dart`
- [ ] **Task 9.2:** Create reusable 2FA components
  - [ ] `lib/presentation/widgets/two_factor_input.dart`
- [ ] **Task 9.3:** Refactor 2FA pages
  - [ ] Update `lib/views/pages/EnterTwoFacCode.dart`

**Files Affected:** `lib/views/pages/EnterTwoFacCode.dart`  
**Assignee:** _TBD_  
**Estimated Effort:** 3-4 days  
**Complexity:** Medium  
**Dependencies:** Task 2 (AuthBloc)  

---

### 10. Create Reusable Auth UI Components
- [ ] **Task 10.1:** Extract common UI patterns
  - [ ] `lib/presentation/widgets/auth/login_form.dart`
  - [ ] `lib/presentation/widgets/auth/auth_button.dart`
  - [ ] `lib/presentation/widgets/auth/biometric_button.dart`
- [ ] **Task 10.2:** Refactor login pages
  - [ ] Update `lib/views/pages/LoginPage2.dart`

**Files Affected:** `lib/views/pages/LoginPage2.dart`  
**Assignee:** _TBD_  
**Estimated Effort:** 2-3 days  
**Complexity:** Medium  
**Dependencies:** Task 2 (AuthBloc)  

---

## 🔷 **LOW PRIORITY ITEMS**

### 11. Add Comprehensive Authentication Testing
- [ ] **Task 11.1:** Unit tests for domain layer
  - [ ] `test/domain/repositories/auth_repository_test.dart`
  - [ ] `test/domain/usecases/login_usecase_test.dart`
- [ ] **Task 11.2:** BLoC tests
  - [ ] `test/bloc/auth/auth_bloc_test.dart`
- [ ] **Task 11.3:** Repository implementation tests
  - [ ] `test/data/repositories/auth_repository_impl_test.dart`
- [ ] **Task 11.4:** Widget tests for auth components
  - [ ] `test/presentation/widgets/auth/login_form_test.dart`

**Files Affected:** New test files  
**Assignee:** _TBD_  
**Estimated Effort:** 3-4 days  
**Complexity:** Low  
**Dependencies:** All previous tasks  

---

### 12. Implement Authentication State Persistence
- [ ] **Task 12.1:** Create auth state service
  - [ ] `lib/core/services/auth_state_service.dart`
- [ ] **Task 12.2:** Update main.dart for state restoration
- [ ] **Task 12.3:** Update AuthBloc for persistence

**Files Affected:** `lib/main.dart`, `lib/bloc/auth/auth_bloc.dart`  
**Assignee:** _TBD_  
**Estimated Effort:** 1-2 days  
**Complexity:** Low  
**Dependencies:** Task 2 (AuthBloc)  

---

### 13. Create Authentication Flow Documentation
- [ ] **Task 13.1:** Architecture documentation
  - [ ] `docs/authentication/README.md`
  - [ ] `docs/authentication/architecture.md`
- [ ] **Task 13.2:** Flow diagrams
  - [ ] `docs/authentication/flows.md`
- [ ] **Task 13.3:** API documentation
  - [ ] Document all auth services and repositories

**Files Affected:** New documentation files  
**Assignee:** _TBD_  
**Estimated Effort:** 1-2 days  
**Complexity:** Low  
**Dependencies:** All implementation tasks  

---

### 14. Add Authentication Analytics
- [ ] **Task 14.1:** Create analytics service
  - [ ] `lib/core/services/auth_analytics_service.dart`
- [ ] **Task 14.2:** Integrate analytics in auth flows
- [ ] **Task 14.3:** Setup analytics dashboard

**Files Affected:** All auth flow files  
**Assignee:** _TBD_  
**Estimated Effort:** 1-2 days  
**Complexity:** Low  
**Dependencies:** Task 2 (AuthBloc)  

---

## 📅 **Implementation Timeline**

### Phase 1: Foundation (Weeks 1-2)
**Target Completion:** _TBD_
- [ ] Task 1: Domain Layer
- [ ] Task 2: AuthBloc
- [ ] Task 3: Dependency Injection

### Phase 2: Data Layer (Weeks 3-4)
**Target Completion:** _TBD_
- [ ] Task 4: Data Layer
- [ ] Task 5: Error Handling
- [ ] Task 6: Token Management

### Phase 3: Services & UI (Weeks 5-6)
**Target Completion:** _TBD_
- [ ] Task 7: Session Management
- [ ] Task 8: Biometric Abstraction
- [ ] Task 9: Two-Factor Authentication
- [ ] Task 10: UI Components

### Phase 4: Quality & Documentation (Weeks 7-8)
**Target Completion:** _TBD_
- [ ] Task 11: Testing
- [ ] Task 12: State Persistence
- [ ] Task 13: Documentation
- [ ] Task 14: Analytics

---

## 🎯 **Expected Benefits**

### SOLID Principles Compliance
- **Single Responsibility:** Each class has one reason to change
- **Open/Closed:** Easy to extend without modifying existing code
- **Liskov Substitution:** Proper inheritance hierarchies
- **Interface Segregation:** Focused, cohesive interfaces
- **Dependency Inversion:** Depend on abstractions, not concretions

### DRY Implementation
- **Reusable Components:** Common auth UI patterns extracted
- **Centralized Logic:** Token and session management in dedicated services
- **Consistent Patterns:** Standardized error handling and state management

### Additional Benefits
- **Testability:** Mockable dependencies and isolated units
- **Maintainability:** Clear architecture and comprehensive documentation
- **Scalability:** Easy to extend with new authentication methods
- **Security:** Proper token and session management
- **Developer Experience:** Clear separation of concerns and dependency injection

---

## 👥 **Team Assignments**

| Task | Assignee | Status | Start Date | Target Date | Notes |
|------|----------|--------|------------|-------------|-------|
| Task 1 | _TBD_ | Not Started | _TBD_ | _TBD_ | Domain layer foundation |
| Task 2 | _TBD_ | Not Started | _TBD_ | _TBD_ | Depends on Task 1 |
| Task 3 | _TBD_ | Not Started | _TBD_ | _TBD_ | Can start after Task 1 |
| Task 4 | _TBD_ | Not Started | _TBD_ | _TBD_ | Depends on Task 1 |
| Task 5 | _TBD_ | Not Started | _TBD_ | _TBD_ | Depends on Task 2, 4 |
| ... | ... | ... | ... | ... | ... |

---

## 🔍 **Code Review Checklist**

### For All Tasks:
- [ ] SOLID principles followed
- [ ] DRY principle applied
- [ ] Proper error handling implemented
- [ ] Unit tests written and passing
- [ ] Documentation updated
- [ ] No breaking changes to existing functionality

### For High Priority Tasks:
- [ ] Architecture review completed
- [ ] Performance impact assessed
- [ ] Security implications reviewed
- [ ] Integration tests added

---

## 🚨 **Risk Assessment**

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Breaking existing auth flows | High | Medium | Comprehensive testing, gradual rollout |
| Performance degradation | Medium | Low | Performance testing, optimization |
| Security vulnerabilities | High | Low | Security review, penetration testing |
| Timeline delays | Medium | Medium | Parallel development, clear dependencies |

---

## 📊 **Metrics to Track**

### Code Quality Metrics
- [ ] Code coverage: Target >90% for auth modules
- [ ] Cyclomatic complexity: Keep <10 per method
- [ ] Technical debt ratio: Reduce by 50%
- [ ] SOLID compliance score: Target >8/10

### Performance Metrics
- [ ] Login time: <2 seconds
- [ ] Token refresh time: <1 second
- [ ] Biometric auth time: <3 seconds
- [ ] Memory usage: No significant increase

---

## 📝 **Notes**

- All tasks should include appropriate unit tests
- Code reviews required for all high and medium priority items
- Update this document as tasks are completed
- Consider breaking large tasks into smaller subtasks if needed
- Ensure backward compatibility during refactoring process
- Regular team sync meetings to discuss progress and blockers
- Document any architectural decisions in ADR format

---

## 📚 **References**

- [Clean Architecture by Robert C. Martin](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [SOLID Principles](https://en.wikipedia.org/wiki/SOLID)
- [Flutter BLoC Pattern](https://bloclibrary.dev/)
- [Dependency Injection in Flutter](https://pub.dev/packages/get_it)
- [Flutter Secure Storage](https://pub.dev/packages/flutter_secure_storage)

---

**Last Updated:** _TBD_
**Document Version:** 1.0
**Next Review Date:** _TBD_
**Maintained By:** Development Team
